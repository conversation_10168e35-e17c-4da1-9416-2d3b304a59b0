import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export default function Payment() {
  const location = useLocation();
  const navigate = useNavigate();
  const bookingData = location.state?.bookingData;

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem("user");
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  const paymentMethods = [
    {
      id: "dana",
      name: "<PERSON>",
      logo: "💳",
      description: "Pay with Dana e-wallet",
    },
    {
      id: "shopee",
      name: "ShopeePay",
      logo: "🛒",
      description: "Pay with ShopeePay",
    },
    {
      id: "bri",
      name: "Bank BRI",
      logo: "🏦",
      description: "Transfer via BRI",
    },
    {
      id: "qris",
      name: "QRIS",
      logo: "📱",
      description: "Scan QR code to pay",
    },
    {
      id: "paypal",
      name: "PayPal",
      logo: "💰",
      description: "Pay with PayPal",
    },
  ];

  // Helper function to get transport price
  const getTransportPrice = (transportType) => {
    const transportOptions = [
      { value: "Medium Car", price: 400000 },
      { value: "Hiace", price: 800000 },
      { value: "Medium Car Full Day", price: 600000 },
      { value: "Hiace Full Day", price: 1000000 },
    ];

    const option = transportOptions.find((opt) => opt.value === transportType);
    return option ? option.price : 0;
  };

  // Calculate order items from booking data
  const calculateOrderItems = () => {
    if (!bookingData) return [];

    const items = [];

    // Add main activity
    if (
      bookingData.selectedActivities &&
      bookingData.selectedActivities.length > 0
    ) {
      const mainActivity = bookingData.selectedActivities[0];
      if (mainActivity) {
        items.push({
          name: mainActivity.name,
          price: mainActivity.price * bookingData.guests,
          guests: bookingData.guests,
          pricePerPerson: mainActivity.price,
        });
      }
    }

    // Add additional activities
    if (
      bookingData.additionalActivities &&
      bookingData.additionalActivities.length > 0
    ) {
      bookingData.additionalActivities.forEach((activity) => {
        if (activity.selectedService) {
          items.push({
            name: activity.selectedService.name,
            price: activity.selectedService.price * activity.guests,
            guests: activity.guests,
            pricePerPerson: activity.selectedService.price,
          });
        }
      });
    }

    // Add transport if selected
    if (
      bookingData.transportData?.needTransport &&
      bookingData.transportData?.transportType
    ) {
      const transportPrice = getTransportPrice(
        bookingData.transportData.transportType
      );
      items.push({
        name: bookingData.transportData.transportType,
        price: transportPrice,
        guests: 1,
        pricePerPerson: transportPrice,
        isTransport: true,
      });
    }

    return items;
  };

  const orderItems = calculateOrderItems();
  const total = bookingData?.total || 0;

  const handlePayNow = async () => {
    if (!selectedPaymentMethod) {
      alert("Please select a payment method");
      return;
    }

    setIsProcessing(true);

    // Simulate payment processing delay
    setTimeout(() => {
      // Save booking to localStorage for history
      const bookings = JSON.parse(localStorage.getItem("bookings") || "[]");
      const newBooking = {
        id: Date.now(),
        ...bookingData,
        paymentMethod: selectedPaymentMethod,
        status: "confirmed",
        paymentDate: new Date().toISOString(),
        userId: user?.id,
      };
      bookings.push(newBooking);
      localStorage.setItem("bookings", JSON.stringify(bookings));

      setIsProcessing(false);
      alert("Payment successful! Your booking has been confirmed.");
      navigate("/history");
    }, 2000);
  };

  if (!bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-ubud-dark-green mb-4">
            No booking data found
          </h2>
          <button
            onClick={() => navigate("/booking")}
            className="bg-ubud-dark-green text-white px-6 py-3 rounded-lg hover:bg-ubud-light-green transition-colors"
          >
            Go to Booking
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={() => navigate("/booking")}
                className="bg-white text-ubud-dark-green px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2 border border-gray-200"
              >
                <span>←</span>
                Back to Booking
              </button>
              <h1 className="text-3xl font-bold text-ubud-dark-green">
                Complete Your Payment
              </h1>
              <div className="w-32"></div> {/* Spacer for centering */}
            </div>
            <p className="text-gray-600 text-center">
              Review your booking details and choose your payment method
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Side - Booking Details */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-ubud-dark-green mb-4 flex items-center">
                  <span className="mr-2">📋</span>
                  Booking Details
                </h3>

                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Name:</span>
                    <span className="font-medium">{bookingData.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Phone:</span>
                    <span className="font-medium">{bookingData.phone}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Date:</span>
                    <span className="font-medium">{bookingData.date}</span>
                  </div>
                </div>

                <div className="border-t pt-4 mt-4">
                  <h4 className="font-semibold text-ubud-dark-green mb-3">
                    Booking Summary
                  </h4>
                  <div className="space-y-3">
                    {orderItems.map((item, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <span className="font-medium text-gray-800">
                              {item.name}
                            </span>
                            <div className="text-xs text-gray-500 mt-1">
                              {item.isTransport ? (
                                "Transport service"
                              ) : (
                                <>
                                  {item.guests} people × Rp.{" "}
                                  {item.pricePerPerson.toLocaleString("id-ID")}
                                </>
                              )}
                            </div>
                          </div>
                          <span className="font-semibold text-ubud-dark-green">
                            Rp. {item.price.toLocaleString("id-ID")}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="border-t pt-3 mt-4">
                    <div className="flex justify-between items-center font-bold text-lg">
                      <span>Total</span>
                      <span className="text-ubud-dark-green">
                        Rp. {total.toLocaleString("id-ID")}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Payment Methods */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-ubud-dark-green mb-4 flex items-center">
                  <span className="mr-2">💳</span>
                  Choose Payment Method
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {paymentMethods.map((method) => (
                    <div
                      key={method.id}
                      onClick={() => setSelectedPaymentMethod(method.id)}
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        selectedPaymentMethod === method.id
                          ? "border-ubud-dark-green bg-ubud-dark-green/5"
                          : "border-gray-200 hover:border-ubud-light-green"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{method.logo}</span>
                        <div>
                          <div className="font-semibold text-ubud-dark-green">
                            {method.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            {method.description}
                          </div>
                        </div>
                        {selectedPaymentMethod === method.id && (
                          <div className="ml-auto">
                            <div className="w-5 h-5 bg-ubud-dark-green rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">✓</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Payment Button */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-lg font-semibold text-ubud-dark-green">
                    Total Payment
                  </span>
                  <span className="text-2xl font-bold text-ubud-dark-green">
                    Rp. {total.toLocaleString("id-ID")}
                  </span>
                </div>

                <button
                  onClick={handlePayNow}
                  disabled={!selectedPaymentMethod || isProcessing}
                  className={`w-full py-4 rounded-lg font-bold text-lg transition-all ${
                    !selectedPaymentMethod || isProcessing
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-ubud-dark-green text-white hover:bg-ubud-light-green shadow-lg hover:shadow-xl"
                  }`}
                >
                  {isProcessing ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Processing Payment...
                    </div>
                  ) : (
                    `Pay with ${
                      selectedPaymentMethod
                        ? paymentMethods.find(
                            (m) => m.id === selectedPaymentMethod
                          )?.name
                        : "Selected Method"
                    }`
                  )}
                </button>

                <div className="mt-4 text-center text-sm text-gray-600">
                  <p>🔒 Your payment is secured with 256-bit SSL encryption</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
