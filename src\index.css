@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Ubud Colors */
:root {
  --ubud-dark-green: #626f47;
  --ubud-light-green: #a4b465;
  --ubud-yellow: #ffcf50;
  --ubud-cream: #fefae0;
}

@layer components {
  /* Ubud Color Components */
  .bg-ubud-dark-green {
    background-color: #626f47 !important;
  }

  .bg-ubud-light-green {
    background-color: #a4b465 !important;
  }

  .bg-ubud-yellow {
    background-color: #ffcf50 !important;
  }

  .bg-ubud-cream {
    background-color: #fefae0 !important;
  }

  .text-ubud-dark-green {
    color: #626f47 !important;
  }

  .text-ubud-light-green {
    color: #a4b465 !important;
  }

  .text-ubud-yellow {
    color: #ffcf50 !important;
  }

  .text-ubud-cream {
    color: #fefae0 !important;
  }

  .border-ubud-dark-green {
    border-color: #626f47 !important;
  }

  .from-ubud-dark-green {
    --tw-gradient-from: #626f47 !important;
    --tw-gradient-to: rgba(98, 111, 71, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .to-ubud-light-green {
    --tw-gradient-to: #a4b465 !important;
  }

  .from-ubud-light-green {
    --tw-gradient-from: #a4b465 !important;
    --tw-gradient-to: rgba(164, 180, 101, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .to-ubud-dark-green {
    --tw-gradient-to: #626f47 !important;
  }

  .hover\:bg-ubud-light-green:hover {
    background-color: #a4b465 !important;
  }

  .focus\:border-ubud-dark-green:focus {
    border-color: #626f47 !important;
  }
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slideUp {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}
