import { useState } from "react";
import { useNavigate } from "react-router-dom";

export default function Booking() {
  const navigate = useNavigate();

  // State untuk modal data diri
  const [showPersonalDataModal, setShowPersonalDataModal] = useState(true);
  const [personalData, setPersonalData] = useState({
    name: "",
    phone: "",
    activityDate: "",
    hotel: "",
    needTransport: null, // true/false
    transportType: "",
    driverPhone: "",
  });

  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    date: "",
    guests: 1,
    mainActivity: "",
  });

  // Dynamic additional activities with individual guest counts
  const [additionalActivities, setAdditionalActivities] = useState([]);

  const [selectedActivities, setSelectedActivities] = useState([null]);

  const services = [
    { id: 1, name: "Trekking and Jeep", price: 150000 },
    { id: 2, name: "Swing and Nest", price: 150000 },
    { id: 3, name: "Rafting", price: 150000 },
    { id: 4, name: "ATV Ride", price: 150000 },
  ];

  // Handler untuk personal data modal
  const handlePersonalDataChange = (e) => {
    const { name, value, type } = e.target;
    setPersonalData({
      ...personalData,
      [name]: type === "radio" ? value === "true" : value,
    });
  };

  const handleTransportChange = (value) => {
    setPersonalData({
      ...personalData,
      needTransport: value,
      transportType: value ? personalData.transportType : "",
      driverPhone: value ? "" : personalData.driverPhone,
    });
  };

  const validatePersonalData = () => {
    const {
      name,
      phone,
      activityDate,
      hotel,
      needTransport,
      transportType,
      driverPhone,
    } = personalData;

    if (!name || !phone || !activityDate || !hotel || needTransport === null) {
      return false;
    }

    if (needTransport && !transportType) {
      return false;
    }

    if (!needTransport && !driverPhone) {
      return false;
    }

    return true;
  };

  const handlePersonalDataSubmit = () => {
    if (!validatePersonalData()) {
      alert("Mohon lengkapi semua data yang diperlukan");
      return;
    }

    // Transfer data ke form booking
    setFormData({
      ...formData,
      name: personalData.name,
      phone: personalData.phone,
      date: personalData.activityDate,
    });

    setShowPersonalDataModal(false);
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  // Add new additional activity
  const addAdditionalActivity = () => {
    const newActivity = {
      id: Date.now(), // Simple ID generation
      activityName: "",
      guests: formData.guests, // Default to main activity guest count
      selectedService: null,
    };
    setAdditionalActivities([...additionalActivities, newActivity]);
  };

  // Remove additional activity
  const removeAdditionalActivity = (id) => {
    setAdditionalActivities(
      additionalActivities.filter((activity) => activity.id !== id)
    );
  };

  // Handle additional activity changes
  const handleAdditionalActivityChange = (id, field, value) => {
    setAdditionalActivities(
      additionalActivities.map((activity) => {
        if (activity.id === id) {
          const updatedActivity = { ...activity, [field]: value };

          // If activity name changed, find the corresponding service
          if (field === "activityName") {
            const service = services.find((s) => s.name === value);
            updatedActivity.selectedService = service || null;
          }

          // If guests field changed, handle it more flexibly
          if (field === "guests") {
            // Allow empty string during typing, but store as number when valid
            if (value === "" || value === "0") {
              updatedActivity.guests = value; // Allow temporary empty/zero state
            } else {
              const numValue = parseInt(value);
              updatedActivity.guests = isNaN(numValue) || numValue < 1 ? 1 : numValue;
            }
          }

          return updatedActivity;
        }
        return activity;
      })
    );
  };

  const handleActivityChange = (index, activityName) => {
    const activity = services.find((s) => s.name === activityName);
    const newSelectedActivities = [...selectedActivities];
    newSelectedActivities[index] = activity;
    setSelectedActivities(newSelectedActivities);

    if (index === 0) {
      setFormData({ ...formData, mainActivity: activityName });
    }
  };

  const calculateTotal = () => {
    // Calculate main activity cost
    const mainActivityCost = selectedActivities.reduce((total, activity, index) => {
      if (activity && index === 0) {
        return total + activity.price * formData.guests;
      }
      return total;
    }, 0);

    // Calculate additional activities cost
    const additionalCost = additionalActivities.reduce((total, activity) => {
      if (activity.selectedService) {
        const guests = parseInt(activity.guests) || 1;
        return total + activity.selectedService.price * guests;
      }
      return total;
    }, 0);

    return mainActivityCost + additionalCost;
  };

  const handleMakeBook = () => {
    if (
      !formData.name ||
      !formData.phone ||
      !formData.date ||
      !formData.mainActivity
    ) {
      alert("Please fill in all required fields");
      return;
    }

    const bookingData = {
      ...formData,
      selectedActivities: selectedActivities.filter((a) => a !== null),
      additionalActivities: additionalActivities.filter(
        (a) => a.selectedService !== null
      ),
      total: calculateTotal(),
      bookingDate: new Date().toISOString(),
    };

    navigate("/payment", { state: { bookingData } });
  };

  // Modal Data Diri Component
  const PersonalDataModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green p-6">
          <h2 className="text-2xl font-bold text-white text-center">
            Data Diri Peserta
          </h2>
          <p className="text-ubud-cream text-center mt-2">
            Mohon lengkapi data diri sebelum melanjutkan booking
          </p>
        </div>
        
        <div className="p-6 space-y-4">
          {/* Nama */}
          <div>
            <label className="block text-ubud-dark-green font-semibold mb-2">
              Nama Lengkap *
            </label>
            <input
              type="text"
              name="name"
              value={personalData.name}
              onChange={handlePersonalDataChange}
              placeholder="Masukkan nama lengkap"
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
            />
          </div>

          {/* Nomor Telepon */}
          <div>
            <label className="block text-ubud-dark-green font-semibold mb-2">
              Nomor Telepon *
            </label>
            <input
              type="tel"
              name="phone"
              value={personalData.phone}
              onChange={handlePersonalDataChange}
              placeholder="Masukkan nomor telepon"
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
            />
          </div>

          {/* Tanggal Kegiatan */}
          <div>
            <label className="block text-ubud-dark-green font-semibold mb-2">
              Tanggal Kegiatan *
            </label>
            <input
              type="date"
              name="activityDate"
              value={personalData.activityDate}
              onChange={handlePersonalDataChange}
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
            />
          </div>

          {/* Hotel */}
          <div>
            <label className="block text-ubud-dark-green font-semibold mb-2">
              Hotel *
            </label>
            <input
              type="text"
              name="hotel"
              value={personalData.hotel}
              onChange={handlePersonalDataChange}
              placeholder="Masukkan nama hotel"
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
            />
          </div>

          {/* Transport */}
          <div>
            <label className="block text-ubud-dark-green font-semibold mb-2">
              Apakah memerlukan transport? *
            </label>
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => handleTransportChange(true)}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  personalData.needTransport === true
                    ? "bg-ubud-dark-green text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                Ya
              </button>
              <button
                type="button"
                onClick={() => handleTransportChange(false)}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  personalData.needTransport === false
                    ? "bg-ubud-dark-green text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                Tidak
              </button>
            </div>
          </div>

          {/* Transport Type - hanya muncul jika memerlukan transport */}
          {personalData.needTransport === true && (
            <div>
              <label className="block text-ubud-dark-green font-semibold mb-2">
                Jenis Transport *
              </label>
              <select
                name="transportType"
                value={personalData.transportType}
                onChange={handlePersonalDataChange}
                className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
              >
                <option value="">Pilih jenis transport</option>
                <option value="car">Mobil</option>
                <option value="motorcycle">Motor</option>
                <option value="van">Van</option>
                <option value="bus">Bus</option>
              </select>
            </div>
          )}

          {/* Driver Phone - hanya muncul jika tidak memerlukan transport */}
          {personalData.needTransport === false && (
            <div>
              <label className="block text-ubud-dark-green font-semibold mb-2">
                Nomor Telepon Driver *
              </label>
              <input
                type="tel"
                name="driverPhone"
                value={personalData.driverPhone}
                onChange={handlePersonalDataChange}
                placeholder="Masukkan nomor telepon driver"
                className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
              />
            </div>
          )}

          {/* Tombol Submit */}
          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={handlePersonalDataSubmit}
              className="bg-ubud-dark-green text-white px-8 py-3 rounded-lg hover:bg-ubud-light-green transition-colors font-medium"
            >
              OK, Lanjutkan
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div>
      {showPersonalDataModal && <PersonalDataModal />}
      
      {!showPersonalDataModal && (
        <div className="min-h-screen bg-gradient-to-br from-ubud-light-green to-ubud-dark-green">
          <div className="container mx-auto px-6 py-8">
            <div className="max-w-5xl mx-auto">
              <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl overflow-hidden">
                <div className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green p-6">
                  <h1 className="text-3xl font-bold text-white text-center">
                    Book Your Adventure
                  </h1>
                  <p className="text-ubud-cream text-center mt-2">
                    Fill in the details below to book your exciting activities
                  </p>
                </div>
                {/* Rest of booking form will be added in next edit */}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
